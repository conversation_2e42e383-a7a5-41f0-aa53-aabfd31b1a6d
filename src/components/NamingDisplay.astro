---
// 动态计算当前周记名和日记名的组件
// 基于 ISO 8601 标准计算周数

interface Props {
	showWeekly?: boolean;
	showDaily?: boolean;
	className?: string;
}

const { showWeekly = true, showDaily = true, className = "" } = Astro.props;

// 计算 ISO 8601 周数的函数
function getISOWeek(date: Date): {
	year: number;
	week: number;
	dayOfWeek: number;
} {
	// 创建日期副本避免修改原始日期
	const target = new Date(date.valueOf());
	const dayNr = (date.getDay() + 6) % 7; // 周一为0，周日为6

	// 设置到本周四
	target.setDate(target.getDate() - dayNr + 3);
	const jan4 = new Date(target.getFullYear(), 0, 4);
	const dayDiff = (target.getTime() - jan4.getTime()) / 86400000;

	// 计算周数
	const weekNum =
		1 + Math.round((dayDiff - ((jan4.getDay() + 6) % 7) + 3) / 7);

	return {
		year: target.getFullYear(),
		week: weekNum,
		dayOfWeek: dayNr + 1, // 1-7，周一为1，周日为7
	};
}

// 获取当前时间信息
const now = new Date();
const { year, week, dayOfWeek } = getISOWeek(now);

// 生成命名
const weeklyName = `${year} W${week}`;
const dailyName = `${year} W${week} D${dayOfWeek}`;
---

<div class={`naming-display ${className}`}>
	{
		showWeekly && (
			<div class="naming-item weekly">
				<span class="naming-label">周记:</span>
				<span class="naming-value">{weeklyName}</span>
			</div>
		)
	}
	{
		showDaily && (
			<div class="naming-item daily">
				<span class="naming-label">日记:</span>
				<span class="naming-value">{dailyName}</span>
			</div>
		)
	}
</div>

<style>
	.naming-display {
		margin-bottom: 1.5rem;
		border-radius: 0.375rem;
		background-color: var(--color-global-text);
		background-color: color-mix(
			in srgb,
			var(--color-global-text) 5%,
			transparent
		);
		padding: 1rem;
		border-left: 3px solid var(--color-accent);
	}

	.naming-item {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		font-size: 0.875rem;
	}

	.naming-item:not(:last-child) {
		margin-bottom: 0.5rem;
	}

	.naming-label {
		font-weight: 500;
		color: color-mix(in srgb, var(--color-global-text) 70%, transparent);
		min-width: 3rem;
	}

	.naming-value {
		font-family:
			ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
			"Liberation Mono", "Courier New", monospace;
		font-weight: 600;
		color: var(--color-accent);
		font-size: 0.9rem;
		letter-spacing: 0.025em;
	}

	/* 响应式设计 */
	@media (min-width: 640px) {
		.naming-display {
			display: flex;
			align-items: center;
			gap: 1.5rem;
			padding: 0.75rem;
		}

		.naming-item {
			margin-bottom: 0;
		}

		.naming-item:not(:last-child)::after {
			content: "•";
			display: block;
			margin-left: 1rem;
			color: color-mix(
				in srgb,
				var(--color-global-text) 40%,
				transparent
			);
		}
	}

	/* 深色模式适配 */
	[data-theme="dark"] .naming-display {
		background-color: color-mix(
			in srgb,
			var(--color-global-text) 10%,
			transparent
		);
	}

	/* 悬停效果 */
	.naming-display:hover {
		background-color: color-mix(
			in srgb,
			var(--color-global-text) 8%,
			transparent
		);
		transition: background-color 0.2s ease;
	}

	[data-theme="dark"] .naming-display:hover {
		background-color: color-mix(
			in srgb,
			var(--color-global-text) 15%,
			transparent
		);
	}
</style>
