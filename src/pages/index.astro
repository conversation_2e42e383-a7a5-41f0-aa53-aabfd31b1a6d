---
import { type CollectionEntry, getCollection } from "astro:content";
// import SocialList from "@/components/SocialList.astro";
import PostPreview from "@/components/blog/PostPreview.astro";
import Note from "@/components/note/Note.astro";
import NamingDisplay from "@/components/NamingDisplay.astro";
import { getAllPosts } from "@/data/post";
import PageLayout from "@/layouts/Base.astro";
import { collectionDateSort } from "@/utils/date";

// Posts
const MAX_POSTS = 10;
const allPosts = await getAllPosts();
const allPostsByDate = allPosts
	.sort(collectionDateSort)
	.slice(0, MAX_POSTS) as CollectionEntry<"post">[];

// Pinned Posts, set to a max of 3;
const MAX_PINNED_POSTS = 3;
const pinnedPosts = allPostsByDate
	.filter((p) => p.data.pinned)
	.slice(0, MAX_PINNED_POSTS);

// Notes
const MAX_NOTES = 5;
const allNotes = await getCollection("note");
const latestNotes = allNotes.sort(collectionDateSort).slice(0, MAX_NOTES);
---

<PageLayout meta={{ title: "Home" }}>
	<section>
		<h1 class="title mb-6">Hello!</h1>
		<NamingDisplay />
		<p class="mb-4">I'm Moatkon,This is my blog.</p>
		<!-- <p class="mb-4">这里主要是记录生活,会探索如何做生意,毕竟自己不是顶级打工人,靠着公司不是长久之计。</p> -->
		<blockquote>- 2025-08-03</blockquote>

		<p class="title-min mt-6 mb-6">The way I roll:</p>
		<ul class="list-inside list-disc" role="list">
			<li>If it’s not my business, let them.</li>
		</ul>

		<!-- <SocialList />  -->
	</section>
	{
		pinnedPosts.length > 0 && (
			<section class="mt-16">
				<h2 class="title text-accent mb-6 text-xl">Pinned Posts</h2>
				<ul class="space-y-4" role="list">
					{pinnedPosts.map((p) => (
						<li class="grid gap-1 sm:grid-cols-[auto_1fr]">
							<PostPreview post={p} />
						</li>
					))}
				</ul>
			</section>
		)
	}
	<section class="mt-16">
		<h2 class="title text-accent mb-6 text-xl">
			<a href="/posts/">Posts</a>
		</h2>
		<ul class="space-y-4" role="list">
			{
				allPostsByDate.map((p) => (
					<li class="grid gap-1 sm:grid-cols-[auto_1fr]">
						<PostPreview post={p} />
					</li>
				))
			}
		</ul>
	</section>
	{
		latestNotes.length > 0 && (
			<section class="mt-16">
				<h2 class="title text-accent mb-6 text-xl">
					<a href="/notes/">Notes</a>
				</h2>
				<ul class="space-y-6" role="list">
					{latestNotes.map((note) => (
						<li>
							<Note note={note} as="h3" isPreview />
						</li>
					))}
				</ul>
			</section>
		)
	}
</PageLayout>
