// 测试周数计算的准确性
function getISOWeek(date) {
	// 创建日期副本避免修改原始日期
	const target = new Date(date.valueOf());
	const dayNr = (date.getDay() + 6) % 7; // 周一为0，周日为6
	
	// 设置到本周四
	target.setDate(target.getDate() - dayNr + 3);
	const jan4 = new Date(target.getFullYear(), 0, 4);
	const dayDiff = (target.getTime() - jan4.getTime()) / 86400000;
	
	// 计算周数
	const weekNum = 1 + Math.round((dayDiff - (jan4.getDay() + 6) % 7 + 3) / 7);
	
	return {
		year: target.getFullYear(),
		week: weekNum,
		dayOfWeek: dayNr + 1 // 1-7，周一为1，周日为7
	};
}

// 测试一些已知的日期
const testDates = [
	{ date: new Date('2025-01-01'), expected: { year: 2025, week: 1 } },
	{ date: new Date('2025-01-06'), expected: { year: 2025, week: 2 } }, // 周一
	{ date: new Date('2025-09-09'), expected: { year: 2025, week: 37 } }, // 今天
	{ date: new Date('2025-12-31'), expected: { year: 2026, week: 1 } }, // 可能跨年
];

console.log('测试周数计算:');
testDates.forEach(({ date, expected }) => {
	const result = getISOWeek(date);
	const weeklyName = `${result.year} W${result.week}`;
	const dailyName = `${result.year} W${result.week} D${result.dayOfWeek}`;
	
	console.log(`日期: ${date.toISOString().split('T')[0]}`);
	console.log(`  周记: ${weeklyName}`);
	console.log(`  日记: ${dailyName}`);
	console.log(`  预期周数: ${expected.week}, 实际: ${result.week}`);
	console.log('---');
});

// 测试当前时间
const now = new Date();
const current = getISOWeek(now);
console.log(`当前时间: ${now.toISOString()}`);
console.log(`当前周记: ${current.year} W${current.week}`);
console.log(`当前日记: ${current.year} W${current.week} D${current.dayOfWeek}`);
